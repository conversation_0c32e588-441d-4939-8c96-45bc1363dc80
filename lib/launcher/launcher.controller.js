"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LauncherController = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const launcher_service_1 = require("./launcher.service");
const pces_entities_1 = require("../entities/pces.entities");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const clientIp_decorator_1 = require("../utils/clientIp.decorator");
let LauncherController = class LauncherController {
    constructor(launcherService) {
        this.launcherService = launcherService;
    }
    async getGameUrl(data, ip) {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }
    async getGameUrlWithoutRedirection(data, ip) {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }
    async getPCESGameUrl(data) {
        const url = this.launcherService.buildPCESGameUrl(data);
        return { url };
    }
    async getPCESGameUrlWithoutRedirection(data) {
        const url = this.launcherService.buildPCESGameUrl(data);
        return { url };
    }
};
exports.LauncherController = LauncherController;
__decorate([
    (0, common_1.Get)("/url"),
    (0, common_1.UseFilters)(sw_integration_core_1.ErrorFilter),
    (0, common_1.Redirect)(undefined, common_1.HttpStatus.FOUND),
    __param(0, (0, common_1.Query)(new common_2.ValidationPipe())),
    __param(1, (0, clientIp_decorator_1.ClientIp)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pces_entities_1.IntegrationGameLaunchRequest, String]),
    __metadata("design:returntype", Promise)
], LauncherController.prototype, "getGameUrl", null);
__decorate([
    (0, common_1.Get)("/url/noredirect"),
    (0, common_1.UseFilters)(sw_integration_core_1.ErrorFilter),
    __param(0, (0, common_1.Query)(new common_2.ValidationPipe())),
    __param(1, (0, clientIp_decorator_1.ClientIp)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pces_entities_1.IntegrationGameLaunchRequest, String]),
    __metadata("design:returntype", Promise)
], LauncherController.prototype, "getGameUrlWithoutRedirection", null);
__decorate([
    (0, common_1.Get)("/pces/url"),
    (0, common_1.UseFilters)(sw_integration_core_1.ErrorFilter),
    (0, common_1.Redirect)(undefined, common_1.HttpStatus.FOUND),
    __param(0, (0, common_1.Query)(new common_2.ValidationPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pces_entities_1.IntegrationGameLaunchRequest]),
    __metadata("design:returntype", Promise)
], LauncherController.prototype, "getPCESGameUrl", null);
__decorate([
    (0, common_1.Get)("/pces/url/noredirect"),
    (0, common_1.UseFilters)(sw_integration_core_1.ErrorFilter),
    __param(0, (0, common_1.Query)(new common_2.ValidationPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pces_entities_1.IntegrationGameLaunchRequest]),
    __metadata("design:returntype", Promise)
], LauncherController.prototype, "getPCESGameUrlWithoutRedirection", null);
exports.LauncherController = LauncherController = __decorate([
    (0, common_1.Controller)("game"),
    __metadata("design:paramtypes", [launcher_service_1.LauncherService])
], LauncherController);
//# sourceMappingURL=launcher.controller.js.map