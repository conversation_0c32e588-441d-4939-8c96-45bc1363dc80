{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": ";;AAEA,MAAM,IAAI,GAAsB;IAC5B,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,2CAA2C;IAC5F,cAAc,EAAE;QACZ,OAAO,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK;QAChD,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;KACrC;IACD,SAAS,EAAE;QACP,cAAc,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qCAAqC,IAAI,GAAG;QACzE,eAAe,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,KAAK;QACtE,0BAA0B,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,KAAK;KACjF;IACD,GAAG,EAAE;QACD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;QAC3B,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;QAC7B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa;QAC/B,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;KAC1C;CACJ,CAAC;AAEF,MAAM,EAAE,GAAG;IACP,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,YAAY;IAChD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM;IACxB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;IAChC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,WAAW;IACvC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,IAAI;IACjC,GAAG,EAAE;QACD,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,MAAM;QACtD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,UAAU;KAC3C;IACD,cAAc,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE;IACrD,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,KAAK;IACtD,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,QAAQ;IACxC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,MAAM;IACjD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,MAAM;IACpD,KAAK,EAAE;QACH,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,MAAM;QACjD,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,KAAK;KACjD;IACD,qBAAqB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,KAAK;CAC/E,CAAC;AAEF,MAAM,MAAM,GAAG;IACX,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;IAElD,YAAY,EAAE,GAAY,EAAE;QACxB,OAAO,MAAM,CAAC,WAAW,KAAK,YAAY,CAAC;IAC/C,CAAC;IAED,MAAM,EAAE;QACJ,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI;QACnD,YAAY,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI;QACvD,YAAY,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI;QACvD,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,IAAI;KAClD;IAED,cAAc,EAAE;QACZ,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI;QAC/C,GAAG,EAAE;YACD,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,MAAM;SACjD;KACJ;IAED,OAAO,EAAE;QACL,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,MAAM;QACzC,aAAa,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,SAAS,CAAQ;QACpE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,oBAAoB;QACnE,iBAAiB,EAAE;YACf,UAAU,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa;YACxD,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,kBAAkB;SAC/D;KACJ;IAGD,IAAI,EAAE;QACF,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,cAAc;QAC1D,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,kCAAkC;QAC5E,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,kCAAkC;QAC3F,UAAU,EAAE,IAAI;QAChB,sBAAsB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC;QACvE,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,wCAAwC;QAC/F,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,oCAAoC;KAC5F;IAED,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,MAAM;IACjD,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,MAAM;IACjD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,WAAW;IAC7D,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI;IAE7D,sBAAsB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,GAAG;IACpE,qBAAqB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,CAAC;IAEjE,WAAW,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,kBAAkB,CAAC;IAEtF,IAAI;IACJ,EAAE;CACL,CAAC;AAEF,kBAAe,MAAM,CAAC"}