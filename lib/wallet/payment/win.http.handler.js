"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WinHttpHandler = void 0;
const baseHttp_handler_1 = require("../../utils/baseHttp.handler");
const pces_utils_1 = require("../../utils/pces.utils");
const common_1 = require("@nestjs/common");
let WinHttpHandler = class WinHttpHandler extends baseHttp_handler_1.BaseHttpHandler {
    async build(req) {
        const gameTokenData = this.getGameTokenData(req);
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["totalWin", "transactionId", "roundPID"]);
        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        const winAmount = this.sanitizeAmount((0, pces_utils_1.sumMajorUnits)(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("win");
        const creditRequest = this.buildPCESRequest(customer, token, {
            gameId,
            amount: winAmount,
            currency,
            betId,
            trxId,
            freespin: this.buildFreespinInfo(req.freeSpinData)
        });
        return this.buildHttpRequest({
            endpoint: "credit",
            method: "post",
            payload: creditRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        });
    }
    async parse(response) {
        const pcesResponse = this.parseHttpResponse(response);
        return {
            totalAmount: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0)),
            cashAmount: this.sanitizeAmount(pcesResponse.balance),
            bonusAmount: this.sanitizeAmount(pcesResponse.bonusBalance || 0),
            currency: pcesResponse.currency
        };
    }
};
exports.WinHttpHandler = WinHttpHandler;
exports.WinHttpHandler = WinHttpHandler = __decorate([
    (0, common_1.Injectable)()
], WinHttpHandler);
//# sourceMappingURL=win.http.handler.js.map