"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceHttpHandler = void 0;
const baseHttp_handler_1 = require("../../utils/baseHttp.handler");
const common_1 = require("@nestjs/common");
let BalanceHttpHandler = class BalanceHttpHandler extends baseHttp_handler_1.BaseHttpHandler {
    async build(req) {
        const gameTokenData = this.getGameTokenData(req);
        this.validateRequiredFields(gameTokenData, ["customer", "token"]);
        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const authRequest = this.buildPCESRequest(customer, token);
        return this.buildHttpRequest({
            endpoint: "auth",
            method: "post",
            payload: authRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        });
    }
    async parse(response) {
        const pcesResponse = this.parseHttpResponse(response);
        return {
            totalAmount: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0)),
            cashAmount: this.sanitizeAmount(pcesResponse.balance),
            bonusAmount: this.sanitizeAmount(pcesResponse.bonusBalance || 0),
            currency: pcesResponse.currency
        };
    }
};
exports.BalanceHttpHandler = BalanceHttpHandler;
exports.BalanceHttpHandler = BalanceHttpHandler = __decorate([
    (0, common_1.Injectable)()
], BalanceHttpHandler);
//# sourceMappingURL=balance.http.handler.js.map