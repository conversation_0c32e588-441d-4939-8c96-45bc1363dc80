"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
const bet_http_handler_1 = require("./bet.http.handler");
const win_http_handler_1 = require("./win.http.handler");
const balance_http_handler_1 = require("./balance.http.handler");
const debitCredit_http_handler_1 = require("./debitCredit.http.handler");
const rollback_http_handler_1 = require("./rollback.http.handler");
const promo_http_handler_1 = require("./promo.http.handler");
const sw_utils_1 = require("@skywind-group/sw-utils");
const pces_errors_1 = require("../../errors/pces.errors");
const log = sw_utils_1.logging.logger("PaymentService");
const { measure } = sw_utils_1.measures;
let PaymentService = class PaymentService {
    constructor(httpGateway, betHandler, winHandler, balanceHandler, debitCreditHandler, rollbackHandler, promoHandler) {
        this.httpGateway = httpGateway;
        this.betHandler = betHandler;
        this.winHandler = winHandler;
        this.balanceHandler = balanceHandler;
        this.debitCreditHandler = debitCreditHandler;
        this.rollbackHandler = rollbackHandler;
        this.promoHandler = promoHandler;
    }
    async getBalance(req) {
        return await this.httpGateway.request(req, this.balanceHandler);
    }
    async commitBetPayment(req) {
        try {
            if (!req.request.bet || req.request.bet === 0) {
                log.info("Fetch balance instead of zero-bet");
                return this.getBalance(req);
            }
            if (req.request.totalWin && req.request.totalWin > 0) {
                log.info("Using debit-credit for combined bet and win");
                return await this.httpGateway.request(req, this.debitCreditHandler);
            }
            else {
                return await this.httpGateway.request(req, this.betHandler);
            }
        }
        catch (err) {
            if (this.isRefundNeeded(req, err)) {
                log.info(err, "Refund PCES bet");
                throw new sw_wallet_adapter_core_1.RequireRefundBetError();
            }
            if (this.isRollbackNeeded(req, err)) {
                log.info(err, "Rollback PCES bet");
                await this.rollbackTransaction(req);
            }
            log.info(err, "Bet payment failed");
            throw err;
        }
    }
    async commitWinPayment(req) {
        let balance;
        if (this.isWinCommittable(req)) {
            try {
                balance = await this.httpGateway.request(req, this.winHandler);
            }
            catch (err) {
                if (this.isBetAlreadySettled(err) && req.request.retry) {
                    log.warn(err, "Bet is already settled. Fetch balance instead");
                    return this.getBalance(req);
                }
                throw err;
            }
        }
        else {
            log.info("Fetch balance instead of zero-win");
            balance = await this.getBalance(req);
        }
        return balance;
    }
    async commitJackpotWinPayment(req) {
        const promoRequest = {
            ...req,
            promoType: "JPW",
            promoRef: req.request.transactionId.publicId
        };
        return await this.httpGateway.request(promoRequest, this.promoHandler);
    }
    async commitBonusPayment(req) {
        const promoType = this.mapBonusToPromoType(req.request.bonusType);
        const promoRequest = {
            ...req,
            promoType,
            promoRef: req.request.transactionId.publicId
        };
        return await this.httpGateway.request(promoRequest, this.promoHandler);
    }
    async getBalances(req) {
        const balance = await this.getBalance(req);
        return {
            totalBalance: balance,
            cashBalance: {
                totalAmount: balance.cashAmount || 0,
                currency: balance.currency
            },
            bonusBalance: {
                totalAmount: balance.bonusAmount || 0,
                currency: balance.currency
            }
        };
    }
    async rollbackTransaction(req) {
        try {
            return await this.httpGateway.request(req, this.rollbackHandler);
        }
        catch (err) {
            log.error(err, "Failed to rollback transaction");
            return this.getBalance(req);
        }
    }
    isWinCommittable(req) {
        return req.request.totalWin > 0;
    }
    isRefundNeeded(req, err) {
        return (0, pces_errors_1.refundCondition)(err);
    }
    isRollbackNeeded(req, err) {
        return (0, pces_errors_1.rollbackCondition)(err);
    }
    isBetAlreadySettled(err) {
        return err instanceof pces_errors_1.PCESBetAlreadySettledError;
    }
    mapBonusToPromoType(bonusType) {
        switch (bonusType?.toLowerCase()) {
            case "freespin":
            case "freespins":
                return "FSW";
            case "cashback":
                return "CB";
            case "tournament":
                return "TW";
            case "reward":
                return "RW";
            case "rakeback":
                return "RB";
            default:
                return "RW";
        }
    }
};
exports.PaymentService = PaymentService;
__decorate([
    measure({ name: "PaymentService.getBalance", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentService.prototype, "getBalance", null);
__decorate([
    measure({ name: "PaymentService.commitBetPayment", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentService.prototype, "commitBetPayment", null);
__decorate([
    measure({ name: "PaymentService.commitWinPayment", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentService.prototype, "commitWinPayment", null);
__decorate([
    measure({ name: "PaymentService.commitJackpotWinPayment", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentService.prototype, "commitJackpotWinPayment", null);
__decorate([
    measure({ name: "PaymentService.commitBonusPayment", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentService.prototype, "commitBonusPayment", null);
__decorate([
    measure({ name: "PaymentService.rollbackTransaction", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentService.prototype, "rollbackTransaction", null);
exports.PaymentService = PaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [sw_integration_core_1.HttpGateway,
        bet_http_handler_1.BetHttpHandler,
        win_http_handler_1.WinHttpHandler,
        balance_http_handler_1.BalanceHttpHandler,
        debitCredit_http_handler_1.DebitCreditHttpHandler,
        rollback_http_handler_1.RollbackHttpHandler,
        promo_http_handler_1.PromoHttpHandler])
], PaymentService);
//# sourceMappingURL=payment.service.js.map