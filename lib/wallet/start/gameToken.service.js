"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameTokenService = void 0;
const common_1 = require("@nestjs/common");
const sw_utils_1 = require("@skywind-group/sw-utils");
const pces_errors_1 = require("../../errors/pces.errors");
const pces_utils_1 = require("../../utils/pces.utils");
const log = sw_utils_1.logging.logger("GameTokenService");
const { measure } = sw_utils_1.measures;
let GameTokenService = class GameTokenService {
    async createGameToken(request) {
        this.validateRequest(request);
        const gameTokenData = {
            playerCode: request.customer || "",
            currency: (0, pces_utils_1.formatCurrency)(request.currency, request.demo),
            gameCode: (0, pces_utils_1.sanitizeGameId)(request.gameId),
            customer: request.customer || "",
            token: request.token || "",
            gameId: (0, pces_utils_1.sanitizeGameId)(request.gameId),
            demo: request.demo,
            platform: (0, pces_utils_1.parsePlatformType)(request.platform),
            language: request.lang.toLowerCase(),
            country: request.country || "",
            trader: request.trader,
            tableId: request.tableId,
            lobby: request.lobby
        };
        log.info("Created game token for PCES", {
            customer: gameTokenData.customer,
            gameId: gameTokenData.gameId,
            demo: gameTokenData.demo
        });
        return gameTokenData;
    }
    validateRequest(request) {
        if (!request.gameId || request.gameId.trim() === "") {
            throw new pces_errors_1.ValidationError("Game ID is required");
        }
        if (!request.lang || request.lang.trim() === "") {
            throw new pces_errors_1.ValidationError("Language is required");
        }
        if (!request.trader || request.trader.trim() === "") {
            throw new pces_errors_1.ValidationError("Trader is required");
        }
        if (!request.demo && (!request.customer || !request.token)) {
            throw new pces_errors_1.ValidationError("Customer and token are required for real money games");
        }
        if (!request.demo && (!request.currency || request.currency.trim() === "")) {
            throw new pces_errors_1.ValidationError("Currency is required for real money games");
        }
    }
};
exports.GameTokenService = GameTokenService;
__decorate([
    measure({ name: "GameTokenService.createGameToken", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameTokenService.prototype, "createGameToken", null);
exports.GameTokenService = GameTokenService = __decorate([
    (0, common_1.Injectable)()
], GameTokenService);
//# sourceMappingURL=gameToken.service.js.map