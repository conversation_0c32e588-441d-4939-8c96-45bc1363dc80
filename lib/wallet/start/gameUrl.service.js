"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameUrlService = void 0;
const common_1 = require("@nestjs/common");
const sw_utils_1 = require("@skywind-group/sw-utils");
const pces_utils_1 = require("../../utils/pces.utils");
const _config_1 = require("../../config");
const log = sw_utils_1.logging.logger("GameUrlService");
const { measure } = sw_utils_1.measures;
let GameUrlService = class GameUrlService {
    async createGameUrl(request, gameTokenData) {
        const baseUrl = _config_1.default.http.operatorUrl;
        let gameParams;
        if (gameTokenData.demo) {
            gameParams = (0, pces_utils_1.createDemoGameParams)(gameTokenData.gameId, gameTokenData.language, gameTokenData.platform, gameTokenData.trader, gameTokenData.lobby, gameTokenData.tableId);
        }
        else {
            gameParams = (0, pces_utils_1.createRealGameParams)(gameTokenData.currency, gameTokenData.customer, gameTokenData.gameId, gameTokenData.language, gameTokenData.platform, gameTokenData.token, gameTokenData.trader, gameTokenData.country, gameTokenData.lobby, gameTokenData.tableId);
        }
        const gameUrl = (0, pces_utils_1.buildGameUrl)(`${baseUrl}/casino-engine/game`, gameParams);
        log.info("Created game URL for PCES", {
            customer: gameTokenData.customer,
            gameId: gameTokenData.gameId,
            demo: gameTokenData.demo,
            url: gameUrl
        });
        return gameUrl;
    }
};
exports.GameUrlService = GameUrlService;
__decorate([
    measure({ name: "GameUrlService.createGameUrl", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], GameUrlService.prototype, "createGameUrl", null);
exports.GameUrlService = GameUrlService = __decorate([
    (0, common_1.Injectable)()
], GameUrlService);
//# sourceMappingURL=gameUrl.service.js.map