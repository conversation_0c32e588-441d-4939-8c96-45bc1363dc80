"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletModule = void 0;
const common_1 = require("@nestjs/common");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const _config_1 = require("../config");
const payment_service_1 = require("./payment/payment.service");
const balance_http_handler_1 = require("./payment/balance.http.handler");
const bet_http_handler_1 = require("./payment/bet.http.handler");
const win_http_handler_1 = require("./payment/win.http.handler");
const debitCredit_http_handler_1 = require("./payment/debitCredit.http.handler");
const rollback_http_handler_1 = require("./payment/rollback.http.handler");
const promo_http_handler_1 = require("./payment/promo.http.handler");
const gameToken_service_1 = require("./start/gameToken.service");
const gameUrl_service_1 = require("./start/gameUrl.service");
let WalletModule = class WalletModule {
};
exports.WalletModule = WalletModule;
exports.WalletModule = WalletModule = __decorate([
    (0, common_1.Module)({
        providers: [
            gameToken_service_1.GameTokenService,
            gameUrl_service_1.GameUrlService,
            bet_http_handler_1.BetHttpHandler,
            win_http_handler_1.WinHttpHandler,
            debitCredit_http_handler_1.DebitCreditHttpHandler,
            rollback_http_handler_1.RollbackHttpHandler,
            promo_http_handler_1.PromoHttpHandler,
            balance_http_handler_1.BalanceHttpHandler,
            payment_service_1.PaymentService
        ],
        exports: [
            gameToken_service_1.GameTokenService,
            gameUrl_service_1.GameUrlService,
            bet_http_handler_1.BetHttpHandler,
            win_http_handler_1.WinHttpHandler,
            debitCredit_http_handler_1.DebitCreditHttpHandler,
            rollback_http_handler_1.RollbackHttpHandler,
            promo_http_handler_1.PromoHttpHandler,
            balance_http_handler_1.BalanceHttpHandler,
            payment_service_1.PaymentService
        ],
        imports: [
            sw_integration_core_1.StartGameModule.registerWithConfig({
                http: { gatewayConfig: _config_1.default.http },
                createGameToken: gameToken_service_1.GameTokenService,
                createGameURL: gameUrl_service_1.GameUrlService
            }, [WalletModule]),
            sw_integration_core_1.PaymentModule.registerWithConfig({
                http: { gatewayConfig: _config_1.default.http },
                payment: payment_service_1.PaymentService,
                jackpotPayment: payment_service_1.PaymentService,
                bonusPayment: payment_service_1.PaymentService
            }, [WalletModule])
        ]
    })
], WalletModule);
//# sourceMappingURL=wallet.module.js.map