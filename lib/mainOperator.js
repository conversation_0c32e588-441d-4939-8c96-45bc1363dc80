"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sw_utils_1 = require("@skywind-group/sw-utils");
sw_utils_1.measures.measureProvider.baseInstrument();
require("module-alias/register");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const _config_1 = require("./config");
const operator_module_1 = require("./operator/modules/operator.module");
(0, sw_integration_core_1.bootstrapServer)({
    serviceName: "sw-pces-operator",
    versionFile: "./lib/version",
    module: operator_module_1.OperatorModule,
    internalPort: _config_1.default.internalServer.port,
    port: _config_1.default.server.operatorPort,
    secureKeys: _config_1.default.securedKeys
});
//# sourceMappingURL=mainOperator.js.map