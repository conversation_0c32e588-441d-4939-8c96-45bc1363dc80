{"version": 3, "file": "baseHttp.handler.js", "sourceRoot": "", "sources": ["../../src/utils/baseHttp.handler.ts"], "names": [], "mappings": ";;;AAGA,uCAA6B;AAE7B,uDAAuD;AACvD,6CAAwG;AAUxG,MAAsB,eAAe;IACvB,gBAAgB,CAAC,QAAgB,EAAE,KAAa;QACtD,OAAO;YACH,QAAQ;YACR,KAAK;SACR,CAAC;IACN,CAAC;IAES,gBAAgB,CAAC,OAA2B;QAClD,MAAM,GAAG,GAAG,IAAA,4BAAe,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAG9C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAA,8BAAiB,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAGvE,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,OAAO;YACH,GAAG;YACH,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,IAAA,6BAAgB,EAAC,IAAI,CAAC;YAC/B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,OAAO,EAAE,iBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;YAC3C,KAAK,EAAE,iBAAM,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK;YACvC,GAAG,EAAE,iBAAM,CAAC,IAAI,CAAC,GAAG;YACpB,cAAc,EAAE,OAAO,CAAC,cAAc,KAAK,KAAK;SACnD,CAAC;IACN,CAAC;IAES,iBAAiB,CAAI,QAA6B;QACxD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;QAGnC,IAAI,CAAC,IAAA,0BAAa,EAAC,YAAY,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAA,8BAAgB,EAAC;gBACnB,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,CAAC;gBAC7B,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,eAAe;aACjD,EAAE,QAAQ,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,YAAiB,CAAC;IAC7B,CAAC;IAES,gBAAgB,CAAC,GAAQ;QAC/B,OAAO,GAAG,CAAC,aAAa,IAAI,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;IAC3D,CAAC;IAES,gBAAgB,CACtB,QAAgB,EAChB,KAAa,EACb,mBAAwB,EAAE;QAE1B,OAAO;YACH,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC;YACzC,GAAG,gBAAgB;SACtB,CAAC;IACN,CAAC;IAES,sBAAsB,CAAC,GAAQ,EAAE,MAAgB;QACvD,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,4BAA4B,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAES,cAAc,CAAC,MAAc;QAEnC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC1C,CAAC;IAES,cAAc,CAAC,QAAgB,EAAE,IAAa;QACpD,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAES,qBAAqB,CAAC,SAAiB,KAAK;QAClD,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChF,CAAC;IAES,aAAa,CAAC,OAAe;QACnC,OAAO,OAAO,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAC1C,CAAC;IAES,wBAAwB,CAAC,aAAkB;QACjD,OAAO,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,UAAU,CAAC;IAC9D,CAAC;IAES,wBAAwB,CAAC,aAAkB;QACjD,OAAO,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,YAAY,CAAC;IAC7D,CAAC;IAES,aAAa,CAAC,aAAkB;QACtC,OAAO,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC;IAC1D,CAAC;IAES,eAAe,CAAC,aAAkB;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;IACpF,CAAC;IAES,iBAAiB,CAAC,YAAkB;QAC1C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO;YACH,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,KAAK;YAC1C,eAAe,EAAE,YAAY,CAAC,eAAe;YAC7C,aAAa,EAAE,YAAY,CAAC,aAAa;SAC5C,CAAC;IACN,CAAC;IAES,cAAc,CAAC,SAAkB,EAAE,QAAiB,EAAE,YAAkB;QAC9E,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO;YACH,SAAS;YACT,QAAQ;YACR,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;SACrD,CAAC;IACN,CAAC;CACJ;AA5HD,0CA4HC;AAEY,QAAA,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC"}