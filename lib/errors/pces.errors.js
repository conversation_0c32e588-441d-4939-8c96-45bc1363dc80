"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refundCondition = exports.rollbackCondition = exports.retryCondition = exports.pcesUnauthorizedRequest = exports.pcesPlayerSuspended = exports.pcesBetAlreadySettled = exports.pcesGameNotFound = exports.pcesAuthenticationFailed = exports.pcesTokenExpired = exports.pcesInsufficientFunds = exports.PCESInvalidRequestError = exports.PCESHashValidationError = exports.PCESTokenExpiredError = exports.PCESPlayerSuspendedError = exports.PCESCustomerNotFoundError = exports.PCESBettingOffError = exports.PCESLimitReachedError = exports.PCESGameNotFoundError = exports.PCESBetAlreadySettledError = exports.ValidationError = exports.TransactionNotFound = exports.InsufficientBalanceError = exports.AuthenticateFailedError = exports.GeneralError = void 0;
exports.mapPCESToSWError = mapPCESToSWError;
const operator_entities_1 = require("../entities/operator.entities");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
class GeneralError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "General error") {
        super(500, 5000, message, sw_wallet_adapter_core_1.ERROR_LEVEL.ERROR);
    }
}
exports.GeneralError = GeneralError;
class AuthenticateFailedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Authentication failed") {
        super(401, 4010, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.AuthenticateFailedError = AuthenticateFailedError;
class InsufficientBalanceError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Insufficient balance") {
        super(400, 4001, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.InsufficientBalanceError = InsufficientBalanceError;
class TransactionNotFound extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Transaction not found") {
        super(404, 4040, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.TransactionNotFound = TransactionNotFound;
class ValidationError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Validation error") {
        super(400, 4000, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.ValidationError = ValidationError;
function mapPCESToSWError({ code, status }) {
    switch (code) {
        case operator_entities_1.pcesErrorCodes.SUCCESS:
            return new GeneralError("Unexpected success code in error handler");
        case operator_entities_1.pcesErrorCodes.UNKNOWN_ERROR:
        case operator_entities_1.pcesErrorCodes.INTERNAL_CACHE_ERROR:
        case operator_entities_1.pcesErrorCodes.DATA_OUT_OF_RANGE:
            return new GeneralError(status || "Unknown error");
        case operator_entities_1.pcesErrorCodes.UNAUTHORIZED_REQUEST:
            return new AuthenticateFailedError("Unauthorized request - invalid hash");
        case operator_entities_1.pcesErrorCodes.NOT_INTEGRATED:
            return new AuthenticateFailedError("Vendor not active");
        case operator_entities_1.pcesErrorCodes.TOKEN_CUSTOMER_MISMATCH:
            return new AuthenticateFailedError("Token was created for another customer");
        case operator_entities_1.pcesErrorCodes.UNSUPPORTED_API_VERSION:
            return new ValidationError("Unsupported API version");
        case operator_entities_1.pcesErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED:
            return new ValidationError("Promotion type not supported");
        case operator_entities_1.pcesErrorCodes.BET_RECORD_NOT_FOUND:
        case operator_entities_1.pcesErrorCodes.TRANSACTION_NOT_FOUND:
            return new TransactionNotFound(status);
        case operator_entities_1.pcesErrorCodes.BET_ALREADY_WON:
        case operator_entities_1.pcesErrorCodes.BET_ALREADY_SETTLED:
            return new PCESBetAlreadySettledError(status);
        case operator_entities_1.pcesErrorCodes.AUTHENTICATION_FAILED:
            return new AuthenticateFailedError(status || "Authentication failed");
        case operator_entities_1.pcesErrorCodes.GAME_NOT_FOUND:
        case operator_entities_1.pcesErrorCodes.INVALID_GAME:
            return new PCESGameNotFoundError(status);
        case operator_entities_1.pcesErrorCodes.BET_LIMIT_REACHED:
        case operator_entities_1.pcesErrorCodes.LOSS_LIMIT_REACHED:
        case operator_entities_1.pcesErrorCodes.SESSION_LIMIT_REACHED:
        case operator_entities_1.pcesErrorCodes.PROFIT_LIMIT_REACHED:
            return new PCESLimitReachedError(status);
        case operator_entities_1.pcesErrorCodes.INVALID_CASINO_VENDOR:
            return new AuthenticateFailedError("Invalid casino vendor");
        case operator_entities_1.pcesErrorCodes.ALL_BET_ARE_OFF:
            return new PCESBettingOffError(status);
        case operator_entities_1.pcesErrorCodes.CUSTOMER_NOT_FOUND:
            return new PCESCustomerNotFoundError(status);
        case operator_entities_1.pcesErrorCodes.INVALID_CURRENCY:
            return new ValidationError("Invalid currency");
        case operator_entities_1.pcesErrorCodes.INSUFFICIENT_FUNDS:
            return new InsufficientBalanceError(status || "Insufficient funds");
        case operator_entities_1.pcesErrorCodes.PLAYER_SUSPENDED:
            return new PCESPlayerSuspendedError(status);
        case operator_entities_1.pcesErrorCodes.REQUIRED_FIELD_MISSING:
            return new ValidationError("Required field missing");
        case operator_entities_1.pcesErrorCodes.TOKEN_NOT_FOUND:
        case operator_entities_1.pcesErrorCodes.TOKEN_TIMEOUT:
        case operator_entities_1.pcesErrorCodes.TOKEN_INVALID:
            return new PCESTokenExpiredError(status);
        case operator_entities_1.pcesErrorCodes.NEGATIVE_DEPOSIT:
        case operator_entities_1.pcesErrorCodes.NEGATIVE_WITHDRAWAL:
            return new ValidationError(status || "Invalid amount");
        default:
            return new GeneralError(status || `Unknown PCES error: ${code}`);
    }
}
class PCESBetAlreadySettledError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Bet already settled") {
        super(400, 40001, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESBetAlreadySettledError = PCESBetAlreadySettledError;
class PCESGameNotFoundError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Game not found") {
        super(404, 40401, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESGameNotFoundError = PCESGameNotFoundError;
class PCESLimitReachedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Limit reached") {
        super(403, 40301, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESLimitReachedError = PCESLimitReachedError;
class PCESBettingOffError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Betting is currently off") {
        super(503, 50301, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESBettingOffError = PCESBettingOffError;
class PCESCustomerNotFoundError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Customer not found") {
        super(404, 40402, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESCustomerNotFoundError = PCESCustomerNotFoundError;
class PCESPlayerSuspendedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Player is suspended") {
        super(403, 40302, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESPlayerSuspendedError = PCESPlayerSuspendedError;
class PCESTokenExpiredError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Token expired or invalid") {
        super(401, 40101, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESTokenExpiredError = PCESTokenExpiredError;
class PCESHashValidationError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Hash validation failed") {
        super(401, 40102, message, sw_wallet_adapter_core_1.ERROR_LEVEL.ERROR);
    }
}
exports.PCESHashValidationError = PCESHashValidationError;
class PCESInvalidRequestError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Invalid request format") {
        super(400, 40002, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESInvalidRequestError = PCESInvalidRequestError;
const pcesInsufficientFunds = () => ({
    code: operator_entities_1.pcesErrorCodes.INSUFFICIENT_FUNDS,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.INSUFFICIENT_FUNDS]
});
exports.pcesInsufficientFunds = pcesInsufficientFunds;
const pcesTokenExpired = () => ({
    code: operator_entities_1.pcesErrorCodes.TOKEN_TIMEOUT,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.TOKEN_TIMEOUT]
});
exports.pcesTokenExpired = pcesTokenExpired;
const pcesAuthenticationFailed = () => ({
    code: operator_entities_1.pcesErrorCodes.AUTHENTICATION_FAILED,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.AUTHENTICATION_FAILED]
});
exports.pcesAuthenticationFailed = pcesAuthenticationFailed;
const pcesGameNotFound = () => ({
    code: operator_entities_1.pcesErrorCodes.GAME_NOT_FOUND,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.GAME_NOT_FOUND]
});
exports.pcesGameNotFound = pcesGameNotFound;
const pcesBetAlreadySettled = () => ({
    code: operator_entities_1.pcesErrorCodes.BET_ALREADY_SETTLED,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.BET_ALREADY_SETTLED]
});
exports.pcesBetAlreadySettled = pcesBetAlreadySettled;
const pcesPlayerSuspended = () => ({
    code: operator_entities_1.pcesErrorCodes.PLAYER_SUSPENDED,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.PLAYER_SUSPENDED]
});
exports.pcesPlayerSuspended = pcesPlayerSuspended;
const pcesUnauthorizedRequest = () => ({
    code: operator_entities_1.pcesErrorCodes.UNAUTHORIZED_REQUEST,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.UNAUTHORIZED_REQUEST]
});
exports.pcesUnauthorizedRequest = pcesUnauthorizedRequest;
const retryCondition = (err) => err instanceof sw_wallet_adapter_core_1.ConnectionError ||
    err instanceof GeneralError ||
    err instanceof sw_wallet_adapter_core_1.MerchantAdapterAPIError;
exports.retryCondition = retryCondition;
const rollbackCondition = (err) => err instanceof sw_wallet_adapter_core_1.ConnectionError ||
    err instanceof GeneralError ||
    err instanceof PCESBettingOffError ||
    err instanceof PCESLimitReachedError ||
    err instanceof PCESPlayerSuspendedError;
exports.rollbackCondition = rollbackCondition;
const refundCondition = (err) => err instanceof PCESTokenExpiredError ||
    err instanceof PCESCustomerNotFoundError ||
    err instanceof AuthenticateFailedError;
exports.refundCondition = refundCondition;
//# sourceMappingURL=pces.errors.js.map