"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refundCondition = exports.rollbackCondition = exports.retryCondition = exports.pcesUnauthorizedRequest = exports.pcesPlayerSuspended = exports.pcesBetAlreadySettled = exports.pcesGameNotFound = exports.pcesAuthenticationFailed = exports.pcesTokenExpired = exports.pcesInsufficientFunds = exports.PCESInvalidRequestError = exports.PCESHashValidationError = exports.PCESTokenExpiredError = exports.PCESPlayerSuspendedError = exports.PCESCustomerNotFoundError = exports.PCESBettingOffError = exports.PCESLimitReachedError = exports.PCESGameNotFoundError = exports.PCESBetAlreadySettledError = void 0;
exports.mapPCESToSWError = mapPCESToSWError;
const operator_entities_1 = require("../entities/operator.entities");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
const sw_utils_1 = require("@skywind-group/sw-utils");
function mapPCESToSWError({ code, status }, response) {
    switch (code) {
        case operator_entities_1.pcesErrorCodes.SUCCESS:
            return new sw_wallet_adapter_core_1.GeneralError("Unexpected success code in error handler");
        case operator_entities_1.pcesErrorCodes.UNKNOWN_ERROR:
        case operator_entities_1.pcesErrorCodes.INTERNAL_CACHE_ERROR:
        case operator_entities_1.pcesErrorCodes.DATA_OUT_OF_RANGE:
            return new sw_wallet_adapter_core_1.GeneralError(status || "Unknown error");
        case operator_entities_1.pcesErrorCodes.UNAUTHORIZED_REQUEST:
            return new sw_wallet_adapter_core_1.AuthenticateFailedError("Unauthorized request - invalid hash");
        case operator_entities_1.pcesErrorCodes.NOT_INTEGRATED:
            return new sw_wallet_adapter_core_1.AuthenticateFailedError("Vendor not active");
        case operator_entities_1.pcesErrorCodes.TOKEN_CUSTOMER_MISMATCH:
            return new sw_wallet_adapter_core_1.AuthenticateFailedError("Token was created for another customer");
        case operator_entities_1.pcesErrorCodes.UNSUPPORTED_API_VERSION:
            return new sw_wallet_adapter_core_1.ValidationError("Unsupported API version");
        case operator_entities_1.pcesErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED:
            return new sw_wallet_adapter_core_1.ValidationError("Promotion type not supported");
        case operator_entities_1.pcesErrorCodes.BET_RECORD_NOT_FOUND:
        case operator_entities_1.pcesErrorCodes.TRANSACTION_NOT_FOUND:
            return new sw_wallet_adapter_core_1.TransactionNotFound(status);
        case operator_entities_1.pcesErrorCodes.BET_ALREADY_WON:
        case operator_entities_1.pcesErrorCodes.BET_ALREADY_SETTLED:
            return new PCESBetAlreadySettledError(status);
        case operator_entities_1.pcesErrorCodes.AUTHENTICATION_FAILED:
            return new sw_wallet_adapter_core_1.AuthenticateFailedError(status || "Authentication failed");
        case operator_entities_1.pcesErrorCodes.GAME_NOT_FOUND:
        case operator_entities_1.pcesErrorCodes.INVALID_GAME:
            return new PCESGameNotFoundError(status);
        case operator_entities_1.pcesErrorCodes.BET_LIMIT_REACHED:
        case operator_entities_1.pcesErrorCodes.LOSS_LIMIT_REACHED:
        case operator_entities_1.pcesErrorCodes.SESSION_LIMIT_REACHED:
        case operator_entities_1.pcesErrorCodes.PROFIT_LIMIT_REACHED:
            return new PCESLimitReachedError(status);
        case operator_entities_1.pcesErrorCodes.INVALID_CASINO_VENDOR:
            return new sw_wallet_adapter_core_1.AuthenticateFailedError("Invalid casino vendor");
        case operator_entities_1.pcesErrorCodes.ALL_BET_ARE_OFF:
            return new PCESBettingOffError(status);
        case operator_entities_1.pcesErrorCodes.CUSTOMER_NOT_FOUND:
            return new PCESCustomerNotFoundError(status);
        case operator_entities_1.pcesErrorCodes.INVALID_CURRENCY:
            return new sw_wallet_adapter_core_1.ValidationError("Invalid currency");
        case operator_entities_1.pcesErrorCodes.INSUFFICIENT_FUNDS:
            return new sw_wallet_adapter_core_1.InsufficientBalanceError(status || "Insufficient funds");
        case operator_entities_1.pcesErrorCodes.PLAYER_SUSPENDED:
            return new PCESPlayerSuspendedError(status);
        case operator_entities_1.pcesErrorCodes.REQUIRED_FIELD_MISSING:
            return new sw_wallet_adapter_core_1.ValidationError("Required field missing");
        case operator_entities_1.pcesErrorCodes.TOKEN_NOT_FOUND:
        case operator_entities_1.pcesErrorCodes.TOKEN_TIMEOUT:
        case operator_entities_1.pcesErrorCodes.TOKEN_INVALID:
            return new PCESTokenExpiredError(status);
        case operator_entities_1.pcesErrorCodes.NEGATIVE_DEPOSIT:
        case operator_entities_1.pcesErrorCodes.NEGATIVE_WITHDRAWAL:
            return new sw_wallet_adapter_core_1.ValidationError(status || "Invalid amount");
        default:
            return new sw_wallet_adapter_core_1.GeneralError(status || `Unknown PCES error: ${code}`);
    }
}
class PCESBetAlreadySettledError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Bet already settled") {
        super(400, 40001, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESBetAlreadySettledError = PCESBetAlreadySettledError;
class PCESGameNotFoundError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Game not found") {
        super(404, 40401, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESGameNotFoundError = PCESGameNotFoundError;
class PCESLimitReachedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Limit reached") {
        super(403, 40301, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESLimitReachedError = PCESLimitReachedError;
class PCESBettingOffError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Betting is currently off") {
        super(503, 50301, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESBettingOffError = PCESBettingOffError;
class PCESCustomerNotFoundError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Customer not found") {
        super(404, 40402, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESCustomerNotFoundError = PCESCustomerNotFoundError;
class PCESPlayerSuspendedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Player is suspended") {
        super(403, 40302, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESPlayerSuspendedError = PCESPlayerSuspendedError;
class PCESTokenExpiredError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Token expired or invalid") {
        super(401, 40101, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESTokenExpiredError = PCESTokenExpiredError;
class PCESHashValidationError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Hash validation failed") {
        super(401, 40102, message, sw_utils_1.ERROR_LEVEL.ERROR);
    }
}
exports.PCESHashValidationError = PCESHashValidationError;
class PCESInvalidRequestError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Invalid request format") {
        super(400, 40002, message, sw_utils_1.ERROR_LEVEL.WARN);
    }
}
exports.PCESInvalidRequestError = PCESInvalidRequestError;
const pcesInsufficientFunds = () => ({
    code: operator_entities_1.pcesErrorCodes.INSUFFICIENT_FUNDS,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.INSUFFICIENT_FUNDS]
});
exports.pcesInsufficientFunds = pcesInsufficientFunds;
const pcesTokenExpired = () => ({
    code: operator_entities_1.pcesErrorCodes.TOKEN_TIMEOUT,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.TOKEN_TIMEOUT]
});
exports.pcesTokenExpired = pcesTokenExpired;
const pcesAuthenticationFailed = () => ({
    code: operator_entities_1.pcesErrorCodes.AUTHENTICATION_FAILED,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.AUTHENTICATION_FAILED]
});
exports.pcesAuthenticationFailed = pcesAuthenticationFailed;
const pcesGameNotFound = () => ({
    code: operator_entities_1.pcesErrorCodes.GAME_NOT_FOUND,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.GAME_NOT_FOUND]
});
exports.pcesGameNotFound = pcesGameNotFound;
const pcesBetAlreadySettled = () => ({
    code: operator_entities_1.pcesErrorCodes.BET_ALREADY_SETTLED,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.BET_ALREADY_SETTLED]
});
exports.pcesBetAlreadySettled = pcesBetAlreadySettled;
const pcesPlayerSuspended = () => ({
    code: operator_entities_1.pcesErrorCodes.PLAYER_SUSPENDED,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.PLAYER_SUSPENDED]
});
exports.pcesPlayerSuspended = pcesPlayerSuspended;
const pcesUnauthorizedRequest = () => ({
    code: operator_entities_1.pcesErrorCodes.UNAUTHORIZED_REQUEST,
    status: operator_entities_1.pcesStatusMessages[operator_entities_1.pcesErrorCodes.UNAUTHORIZED_REQUEST]
});
exports.pcesUnauthorizedRequest = pcesUnauthorizedRequest;
const retryCondition = (err) => err instanceof sw_wallet_adapter_core_1.ConnectionError ||
    err instanceof sw_wallet_adapter_core_1.GeneralError ||
    err instanceof sw_wallet_adapter_core_1.MerchantAdapterAPIError;
exports.retryCondition = retryCondition;
const rollbackCondition = (err) => err instanceof sw_wallet_adapter_core_1.ConnectionError ||
    err instanceof sw_wallet_adapter_core_1.GeneralError ||
    err instanceof PCESBettingOffError ||
    err instanceof PCESLimitReachedError ||
    err instanceof PCESPlayerSuspendedError;
exports.rollbackCondition = rollbackCondition;
const refundCondition = (err) => err instanceof PCESTokenExpiredError ||
    err instanceof PCESCustomerNotFoundError ||
    err instanceof sw_wallet_adapter_core_1.AuthenticateFailedError;
exports.refundCondition = refundCondition;
//# sourceMappingURL=pces.errors.js.map