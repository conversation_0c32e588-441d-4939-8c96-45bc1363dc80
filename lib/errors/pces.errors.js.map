{"version": 3, "file": "pces.errors.js", "sourceRoot": "", "sources": ["../../src/errors/pces.errors.ts"], "names": [], "mappings": ";;;AAcA,4CAkFC;AAhGD,qEAA4F;AAC5F,kFAS+C;AAE/C,sDAAsD;AAEtD,SAAgB,gBAAgB,CAC5B,EAAE,IAAI,EAAE,MAAM,EAAa,EAC3B,QAA8B;IAE9B,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,kCAAc,CAAC,OAAO;YACvB,OAAO,IAAI,qCAAY,CAAC,0CAA0C,CAAC,CAAC;QAExE,KAAK,kCAAc,CAAC,aAAa,CAAC;QAClC,KAAK,kCAAc,CAAC,oBAAoB,CAAC;QACzC,KAAK,kCAAc,CAAC,iBAAiB;YACjC,OAAO,IAAI,qCAAY,CAAC,MAAM,IAAI,eAAe,CAAC,CAAC;QAEvD,KAAK,kCAAc,CAAC,oBAAoB;YACpC,OAAO,IAAI,gDAAuB,CAAC,qCAAqC,CAAC,CAAC;QAE9E,KAAK,kCAAc,CAAC,cAAc;YAC9B,OAAO,IAAI,gDAAuB,CAAC,mBAAmB,CAAC,CAAC;QAE5D,KAAK,kCAAc,CAAC,uBAAuB;YACvC,OAAO,IAAI,gDAAuB,CAAC,wCAAwC,CAAC,CAAC;QAEjF,KAAK,kCAAc,CAAC,uBAAuB;YACvC,OAAO,IAAI,wCAAe,CAAC,yBAAyB,CAAC,CAAC;QAE1D,KAAK,kCAAc,CAAC,4BAA4B;YAC5C,OAAO,IAAI,wCAAe,CAAC,8BAA8B,CAAC,CAAC;QAE/D,KAAK,kCAAc,CAAC,oBAAoB,CAAC;QACzC,KAAK,kCAAc,CAAC,qBAAqB;YACrC,OAAO,IAAI,4CAAmB,CAAC,MAAM,CAAC,CAAC;QAE3C,KAAK,kCAAc,CAAC,eAAe,CAAC;QACpC,KAAK,kCAAc,CAAC,mBAAmB;YACnC,OAAO,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAElD,KAAK,kCAAc,CAAC,qBAAqB;YACrC,OAAO,IAAI,gDAAuB,CAAC,MAAM,IAAI,uBAAuB,CAAC,CAAC;QAE1E,KAAK,kCAAc,CAAC,cAAc,CAAC;QACnC,KAAK,kCAAc,CAAC,YAAY;YAC5B,OAAO,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE7C,KAAK,kCAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,kCAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,kCAAc,CAAC,qBAAqB,CAAC;QAC1C,KAAK,kCAAc,CAAC,oBAAoB;YACpC,OAAO,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE7C,KAAK,kCAAc,CAAC,qBAAqB;YACrC,OAAO,IAAI,gDAAuB,CAAC,uBAAuB,CAAC,CAAC;QAEhE,KAAK,kCAAc,CAAC,eAAe;YAC/B,OAAO,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAE3C,KAAK,kCAAc,CAAC,kBAAkB;YAClC,OAAO,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAEjD,KAAK,kCAAc,CAAC,gBAAgB;YAChC,OAAO,IAAI,wCAAe,CAAC,kBAAkB,CAAC,CAAC;QAEnD,KAAK,kCAAc,CAAC,kBAAkB;YAClC,OAAO,IAAI,iDAAwB,CAAC,MAAM,IAAI,oBAAoB,CAAC,CAAC;QAExE,KAAK,kCAAc,CAAC,gBAAgB;YAChC,OAAO,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAEhD,KAAK,kCAAc,CAAC,sBAAsB;YACtC,OAAO,IAAI,wCAAe,CAAC,wBAAwB,CAAC,CAAC;QAEzD,KAAK,kCAAc,CAAC,eAAe,CAAC;QACpC,KAAK,kCAAc,CAAC,aAAa,CAAC;QAClC,KAAK,kCAAc,CAAC,aAAa;YAC7B,OAAO,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE7C,KAAK,kCAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,kCAAc,CAAC,mBAAmB;YACnC,OAAO,IAAI,wCAAe,CAAC,MAAM,IAAI,gBAAgB,CAAC,CAAC;QAE3D;YACI,OAAO,IAAI,qCAAY,CAAC,MAAM,IAAI,uBAAuB,IAAI,EAAE,CAAC,CAAC;IACzE,CAAC;AACL,CAAC;AAGD,MAAa,0BAA2B,SAAQ,gCAAO;IACnD,YAAY,OAAO,GAAG,qBAAqB;QACvC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,gEAIC;AAED,MAAa,qBAAsB,SAAQ,gCAAO;IAC9C,YAAY,OAAO,GAAG,gBAAgB;QAClC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,sDAIC;AAED,MAAa,qBAAsB,SAAQ,gCAAO;IAC9C,YAAY,OAAO,GAAG,eAAe;QACjC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,sDAIC;AAED,MAAa,mBAAoB,SAAQ,gCAAO;IAC5C,YAAY,OAAO,GAAG,0BAA0B;QAC5C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,kDAIC;AAED,MAAa,yBAA0B,SAAQ,gCAAO;IAClD,YAAY,OAAO,GAAG,oBAAoB;QACtC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,8DAIC;AAED,MAAa,wBAAyB,SAAQ,gCAAO;IACjD,YAAY,OAAO,GAAG,qBAAqB;QACvC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,4DAIC;AAED,MAAa,qBAAsB,SAAQ,gCAAO;IAC9C,YAAY,OAAO,GAAG,0BAA0B;QAC5C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,sDAIC;AAED,MAAa,uBAAwB,SAAQ,gCAAO;IAChD,YAAY,OAAO,GAAG,wBAAwB;QAC1C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;CACJ;AAJD,0DAIC;AAED,MAAa,uBAAwB,SAAQ,gCAAO;IAChD,YAAY,OAAO,GAAG,wBAAwB;QAC1C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,0DAIC;AAGM,MAAM,qBAAqB,GAAG,GAAc,EAAE,CAAC,CAAC;IACnD,IAAI,EAAE,kCAAc,CAAC,kBAAkB;IACvC,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,kBAAkB,CAAC;CAChE,CAAC,CAAC;AAHU,QAAA,qBAAqB,yBAG/B;AAEI,MAAM,gBAAgB,GAAG,GAAc,EAAE,CAAC,CAAC;IAC9C,IAAI,EAAE,kCAAc,CAAC,aAAa;IAClC,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,aAAa,CAAC;CAC3D,CAAC,CAAC;AAHU,QAAA,gBAAgB,oBAG1B;AAEI,MAAM,wBAAwB,GAAG,GAAc,EAAE,CAAC,CAAC;IACtD,IAAI,EAAE,kCAAc,CAAC,qBAAqB;IAC1C,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,qBAAqB,CAAC;CACnE,CAAC,CAAC;AAHU,QAAA,wBAAwB,4BAGlC;AAEI,MAAM,gBAAgB,GAAG,GAAc,EAAE,CAAC,CAAC;IAC9C,IAAI,EAAE,kCAAc,CAAC,cAAc;IACnC,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,cAAc,CAAC;CAC5D,CAAC,CAAC;AAHU,QAAA,gBAAgB,oBAG1B;AAEI,MAAM,qBAAqB,GAAG,GAAc,EAAE,CAAC,CAAC;IACnD,IAAI,EAAE,kCAAc,CAAC,mBAAmB;IACxC,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,mBAAmB,CAAC;CACjE,CAAC,CAAC;AAHU,QAAA,qBAAqB,yBAG/B;AAEI,MAAM,mBAAmB,GAAG,GAAc,EAAE,CAAC,CAAC;IACjD,IAAI,EAAE,kCAAc,CAAC,gBAAgB;IACrC,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,gBAAgB,CAAC;CAC9D,CAAC,CAAC;AAHU,QAAA,mBAAmB,uBAG7B;AAEI,MAAM,uBAAuB,GAAG,GAAc,EAAE,CAAC,CAAC;IACrD,IAAI,EAAE,kCAAc,CAAC,oBAAoB;IACzC,MAAM,EAAE,sCAAkB,CAAC,kCAAc,CAAC,oBAAoB,CAAC;CAClE,CAAC,CAAC;AAHU,QAAA,uBAAuB,2BAGjC;AAGI,MAAM,cAAc,GAAG,CAAC,GAAY,EAAW,EAAE,CACpD,GAAG,YAAY,wCAAe;IAC9B,GAAG,YAAY,qCAAY;IAC3B,GAAG,YAAY,gDAAuB,CAAC;AAH9B,QAAA,cAAc,kBAGgB;AAEpC,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAW,EAAE,CACvD,GAAG,YAAY,wCAAe;IAC9B,GAAG,YAAY,qCAAY;IAC3B,GAAG,YAAY,mBAAmB;IAClC,GAAG,YAAY,qBAAqB;IACpC,GAAG,YAAY,wBAAwB,CAAC;AAL/B,QAAA,iBAAiB,qBAKc;AAErC,MAAM,eAAe,GAAG,CAAC,GAAY,EAAW,EAAE,CACrD,GAAG,YAAY,qBAAqB;IACpC,GAAG,YAAY,yBAAyB;IACxC,GAAG,YAAY,gDAAuB,CAAC;AAH9B,QAAA,eAAe,mBAGe"}