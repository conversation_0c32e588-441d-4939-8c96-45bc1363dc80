"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryService = void 0;
const common_1 = require("@nestjs/common");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
const operator_entities_1 = require("../../entities/operator.entities");
const _names_1 = require("../../names");
const pces_utils_1 = require("../../utils/pces.utils");
const _config_1 = require("../../config");
const sw_utils_1 = require("@skywind-group/sw-utils");
const log = sw_utils_1.logging.logger("HistoryService");
const { measure } = sw_utils_1.measures;
let HistoryService = class HistoryService {
    constructor(internalAPIService) {
        this.internalAPIService = internalAPIService;
    }
    async getGameHistoryImageUrl(request) {
        const details = await this.getGameHistoryDetails(request);
        if (details.image && details.image.url) {
            return details.image.url;
        }
        return this.buildRoundDetailsUrl(request);
    }
    async getGameHistoryDetails(request) {
        try {
            const url = this.buildRoundDetailsUrl(request);
            const response = await this.internalAPIService.get(url, {});
            if (response.code !== 0) {
                log.warn(`Failed to get round details: ${response.status}`);
                return this.createFallbackResponse(request);
            }
            return response;
        }
        catch (err) {
            log.error(err, "Failed to fetch game history details");
            return this.createFallbackResponse(request);
        }
    }
    buildRoundDetailsUrl(request) {
        return (0, pces_utils_1.buildRoundDetailsUrl)(_config_1.default.pces.roundDetailsUrl, {
            customer: request.customer,
            roundId: request.roundId,
            gameId: request.gameId,
            lang: request.lang,
            trader: request.trader
        });
    }
    createFallbackResponse(request) {
        return {
            code: 0,
            status: "SUCCESS",
            json: {
                gameId: request.gameId,
                gameName: `Game ${request.gameId}`,
                roundId: request.roundId,
                roundDate: new Date().toISOString(),
                betAmount: 0,
                winAmount: 0,
                currency: "USD"
            },
            image: {
                url: `${_config_1.default.pces.roundDetailsUrl}/default-round-image.png`,
                height: 400,
                width: 600
            }
        };
    }
};
exports.HistoryService = HistoryService;
__decorate([
    measure({ name: "HistoryService.getGameHistoryImageUrl", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [operator_entities_1.PCESHistoryRequest]),
    __metadata("design:returntype", Promise)
], HistoryService.prototype, "getGameHistoryImageUrl", null);
__decorate([
    measure({ name: "HistoryService.getGameHistoryDetails", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [operator_entities_1.PCESHistoryRequest]),
    __metadata("design:returntype", Promise)
], HistoryService.prototype, "getGameHistoryDetails", null);
exports.HistoryService = HistoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(_names_1.Names.InternalAPIService)),
    __metadata("design:paramtypes", [sw_wallet_adapter_core_1.InternalAPIService])
], HistoryService);
//# sourceMappingURL=history.service.js.map