"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sw_utils_1 = require("@skywind-group/sw-utils");
sw_utils_1.measures.measureProvider.baseInstrument();
require("module-alias/register");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const _config_1 = require("./config");
const launcher_module_1 = require("./launcher/launcher.module");
(0, sw_integration_core_1.bootstrapServer)({
    serviceName: "sw-pces-launcher",
    versionFile: "./lib/version",
    module: launcher_module_1.LauncherModule,
    internalPort: _config_1.default.internalServer.port,
    port: _config_1.default.server.launcherPort,
    secureKeys: _config_1.default.securedKeys
});
//# sourceMappingURL=mainLauncher.js.map