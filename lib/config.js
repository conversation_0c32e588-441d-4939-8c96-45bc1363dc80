"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const http = {
    operatorUrl: process.env.PCES_BASE_ENGINE_URL || "http://casinoengine.test.pronetgaming.com",
    defaultOptions: {
        timeout: +process.env.PCES_HTTP_TIMEOUT || 10000,
        proxy: process.env.PCES_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.PCES_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.PCES_HTTP_KEEP_ALIVE_SOCKET_TTL || 60000,
        freeSocketKeepAliveTimeout: +process.env.PCES_HTTP_KEEP_ALIVE_TIMEOUT || 30000
    },
    ssl: {
        ca: process.env.PCES_SSL_CA,
        key: process.env.PCES_SSL_KEY,
        cert: process.env.PCES_SSL_CERT,
        password: process.env.PCES_SSL_PASSWORD
    }
};
const db = {
    database: process.env.PGDATABASE || "management",
    user: process.env.PGUSER,
    password: process.env.PGPASSWORD,
    host: process.env.PGHOST || "localhost",
    port: +process.env.PGPORT || 5432,
    ssl: {
        isEnabled: process.env.PG_SECURE_CONNECTION === "true",
        ca: process.env.PG_CA_CERT || "./ca.pem"
    },
    maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
    maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
    schema: process.env.PGSCHEMA || "public",
    syncOnStart: process.env.SYNC_ON_START === "true",
    logEnabled: process.env.PG_MASTER_LOGGING === "true",
    cache: {
        isEnabled: process.env.PG_MASTER_CACHE === "true",
        ttl: +process.env.PG_MASTER_CACHE_TTL || 30000
    },
    connectionTimeoutInMs: +process.env.PG_MASTER_CONNECTION_TIMEOUT_MS || 10000
};
const config = {
    environment: process.env.NODE_ENV || "development",
    isProduction: () => {
        return config.environment === "production";
    },
    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 3000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 3001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 3002,
        mockPort: +process.env.SERVER_MOCK_PORT || 3003
    },
    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4054,
        api: {
            isEnabled: process.env.INTERNAL_API === "true"
        }
    },
    logging: {
        logLevel: process.env.LOG_LEVEL || "info",
        loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || "console"),
        rootLogger: process.env.LOGGING_ROOT_LOGGER || "sw-integration-api",
        defaultSecureKeys: [
            "password", "newPassword", "key", "token", "accessToken",
            "secretKey", "privateToken", "sharedKey", "genericSecretKey"
        ]
    },
    pces: {
        vendorCode: process.env.PCES_VENDOR_CODE || "znidi_gaming",
        genericId: process.env.PCES_GENERIC_ID || "46b0da0cd81423dcdac17d2070b4af16",
        genericSecretKey: process.env.PCES_GENERIC_SECRET_KEY || "86b04d46bb0e81a1131c6e6acd2b7e75",
        apiVersion: "v5",
        tokenExpirationMinutes: +process.env.PCES_TOKEN_EXPIRATION_MINUTES || 5,
        roundDetailsUrl: process.env.PCES_ROUND_DETAILS_URL || "https://api.provider.com/round-details",
        freespinApiUrl: process.env.PCES_FREESPIN_API_URL || "https://api.provider.com/freespins"
    },
    merchantType: process.env.MERCHANT_TYPE || "pces",
    merchantCode: process.env.MERCHANT_CODE || "pces",
    defaultPlayerIp: process.env.DEFAULT_PLAYER_IP || "127.0.0.1",
    defaultJurisdiction: process.env.DEFAULT_JURISDICTION || "UK",
    currencyUnitMultiplier: +process.env.CURRENCY_UNIT_MULTIPLIER || 100,
    messageIdNumberLength: +process.env.MESSAGE_ID_NUMBER_LENGTH || 9,
    securedKeys: ["password", "username", "privateToken", "sharedKey", "genericSecretKey"],
    http,
    db
};
exports.default = config;
//# sourceMappingURL=config.js.map