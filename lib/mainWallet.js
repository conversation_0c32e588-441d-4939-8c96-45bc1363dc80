"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sw_utils_1 = require("@skywind-group/sw-utils");
sw_utils_1.measures.measureProvider.baseInstrument();
require("module-alias/register");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const wallet_module_1 = require("./wallet/wallet.module");
const _config_1 = require("./config");
(0, sw_integration_core_1.bootstrapServer)({
    serviceName: "sw-pces-wallet",
    versionFile: "./lib/version",
    module: wallet_module_1.WalletModule,
    internalPort: _config_1.default.internalServer.port,
    port: _config_1.default.server.walletPort,
    secureKeys: _config_1.default.securedKeys
});
//# sourceMappingURL=mainWallet.js.map