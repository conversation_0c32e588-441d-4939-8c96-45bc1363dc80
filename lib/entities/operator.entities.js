"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pcesStatusMessages = exports.pcesErrorCodes = exports.PCESHistoryRequest = void 0;
const class_validator_1 = require("class-validator");
class PCESHistoryRequest {
}
exports.PCESHistoryRequest = PCESHistoryRequest;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PCESHistoryRequest.prototype, "customer", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PCESHistoryRequest.prototype, "roundId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PCESHistoryRequest.prototype, "gameId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PCESHistoryRequest.prototype, "lang", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PCESHistoryRequest.prototype, "trader", void 0);
exports.pcesErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: -1,
    UNAUTHORIZED_REQUEST: -2,
    NOT_INTEGRATED: -3,
    TOKEN_CUSTOMER_MISMATCH: -4,
    UNSUPPORTED_API_VERSION: -5,
    INTERNAL_CACHE_ERROR: -6,
    PROMOTION_TYPE_NOT_SUPPORTED: -7,
    BET_RECORD_NOT_FOUND: -20120,
    BET_ALREADY_WON: -20112,
    AUTHENTICATION_FAILED: -20101,
    GAME_NOT_FOUND: -20130,
    BET_LIMIT_REACHED: -20201,
    LOSS_LIMIT_REACHED: -20202,
    SESSION_LIMIT_REACHED: -20203,
    PROFIT_LIMIT_REACHED: -20204,
    INVALID_CASINO_VENDOR: -20301,
    ALL_BET_ARE_OFF: -20302,
    INVALID_GAME: -20303,
    CUSTOMER_NOT_FOUND: -20304,
    INVALID_CURRENCY: -20305,
    INSUFFICIENT_FUNDS: -20306,
    PLAYER_SUSPENDED: -20307,
    REQUIRED_FIELD_MISSING: -20308,
    DATA_OUT_OF_RANGE: -20309,
    BET_ALREADY_SETTLED: -20310,
    TOKEN_NOT_FOUND: -20316,
    TOKEN_TIMEOUT: -20311,
    TOKEN_INVALID: -20312,
    TRANSACTION_NOT_FOUND: -20313,
    NEGATIVE_DEPOSIT: -20314,
    NEGATIVE_WITHDRAWAL: -20315
};
exports.pcesStatusMessages = {
    [exports.pcesErrorCodes.SUCCESS]: "SUCCESS",
    [exports.pcesErrorCodes.UNKNOWN_ERROR]: "UNKNOWN_ERROR",
    [exports.pcesErrorCodes.UNAUTHORIZED_REQUEST]: "UNAUTHORIZED_REQUEST",
    [exports.pcesErrorCodes.NOT_INTEGRATED]: "NOT_INTEGRATED",
    [exports.pcesErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "TOKEN_CUSTOMER_MISMATCH",
    [exports.pcesErrorCodes.UNSUPPORTED_API_VERSION]: "UNSUPPORTED_API_VERSION",
    [exports.pcesErrorCodes.INTERNAL_CACHE_ERROR]: "INTERNAL_CACHE_ERROR",
    [exports.pcesErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "PROMOTION_TYPE_NOT_SUPPORTED",
    [exports.pcesErrorCodes.BET_RECORD_NOT_FOUND]: "BET_RECORD_NOT_FOUND",
    [exports.pcesErrorCodes.BET_ALREADY_WON]: "BET_ALREADY_WON",
    [exports.pcesErrorCodes.AUTHENTICATION_FAILED]: "AUTHENTICATION_FAILED",
    [exports.pcesErrorCodes.GAME_NOT_FOUND]: "GAME_NOT_FOUND",
    [exports.pcesErrorCodes.BET_LIMIT_REACHED]: "BET_LIMIT_REACHED",
    [exports.pcesErrorCodes.LOSS_LIMIT_REACHED]: "LOSS_LIMIT_REACHED",
    [exports.pcesErrorCodes.SESSION_LIMIT_REACHED]: "SESSION_LIMIT_REACHED",
    [exports.pcesErrorCodes.PROFIT_LIMIT_REACHED]: "PROFIT_LIMIT_REACHED",
    [exports.pcesErrorCodes.INVALID_CASINO_VENDOR]: "INVALID_CASINO_VENDOR",
    [exports.pcesErrorCodes.ALL_BET_ARE_OFF]: "ALL_BET_ARE_OFF",
    [exports.pcesErrorCodes.INVALID_GAME]: "INVALID_GAME",
    [exports.pcesErrorCodes.CUSTOMER_NOT_FOUND]: "CUSTOMER_NOT_FOUND",
    [exports.pcesErrorCodes.INVALID_CURRENCY]: "INVALID_CURRENCY",
    [exports.pcesErrorCodes.INSUFFICIENT_FUNDS]: "INSUFFICIENT_FUNDS",
    [exports.pcesErrorCodes.PLAYER_SUSPENDED]: "PLAYER_SUSPENDED",
    [exports.pcesErrorCodes.REQUIRED_FIELD_MISSING]: "REQUIRED_FIELD_MISSING",
    [exports.pcesErrorCodes.DATA_OUT_OF_RANGE]: "DATA_OUT_OF_RANGE",
    [exports.pcesErrorCodes.BET_ALREADY_SETTLED]: "BET_ALREADY_SETTLED",
    [exports.pcesErrorCodes.TOKEN_NOT_FOUND]: "TOKEN_NOT_FOUND",
    [exports.pcesErrorCodes.TOKEN_TIMEOUT]: "TOKEN_TIMEOUT",
    [exports.pcesErrorCodes.TOKEN_INVALID]: "TOKEN_INVALID",
    [exports.pcesErrorCodes.TRANSACTION_NOT_FOUND]: "TRANSACTION_NOT_FOUND",
    [exports.pcesErrorCodes.NEGATIVE_DEPOSIT]: "NEGATIVE_DEPOSIT",
    [exports.pcesErrorCodes.NEGATIVE_WITHDRAWAL]: "NEGATIVE_WITHDRAWAL"
};
//# sourceMappingURL=operator.entities.js.map