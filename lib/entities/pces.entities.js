"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationGameLaunchRequest = exports.PCESPromoType = exports.PCESPlatformType = void 0;
const class_validator_1 = require("class-validator");
var PCESPlatformType;
(function (PCESPlatformType) {
    PCESPlatformType["desktop"] = "d";
    PCESPlatformType["mobile"] = "m";
})(PCESPlatformType || (exports.PCESPlatformType = PCESPlatformType = {}));
var PCESPromoType;
(function (PCESPromoType) {
    PCESPromoType["FSW"] = "FSW";
    PCESPromoType["JPW"] = "JPW";
    PCESPromoType["CB"] = "CB";
    PCESPromoType["TW"] = "TW";
    PCESPromoType["RW"] = "RW";
    PCESPromoType["REW"] = "REW";
    PCESPromoType["CDW"] = "CDW";
    PCESPromoType["RB"] = "RB";
})(PCESPromoType || (exports.PCESPromoType = PCESPromoType = {}));
class IntegrationGameLaunchRequest {
}
exports.IntegrationGameLaunchRequest = IntegrationGameLaunchRequest;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "gameId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Boolean)
], IntegrationGameLaunchRequest.prototype, "demo", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "currency", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "customer", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "token", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "lang", void 0);
__decorate([
    (0, class_validator_1.IsIn)([PCESPlatformType.desktop, PCESPlatformType.mobile]),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "platform", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "tableId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "trader", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "lobby", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], IntegrationGameLaunchRequest.prototype, "country", void 0);
//# sourceMappingURL=pces.entities.js.map