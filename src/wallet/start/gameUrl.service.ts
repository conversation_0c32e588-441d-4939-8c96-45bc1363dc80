import { Injectable } from "@nestjs/common";
import { CreateGameUrlSupport } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationInitRequest } from "@entities/pces.entities";
import { logging, measures } from "@skywind-group/sw-utils";
import { buildGameUrl, createDemoGameParams, createRealGameParams } from "@utils/pces.utils";
import config from "@config";

const log = logging.logger("GameUrlService");
const { measure } = measures;

@Injectable()
export class GameUrlService implements CreateGameUrlSupport<IntegrationGameTokenData> {
    
    @measure({ name: "GameUrlService.createGameUrl", isAsync: true })
    public async createGameUrl(
        request: IntegrationInitRequest,
        gameTokenData: IntegrationGameTokenData
    ): Promise<string> {
        const baseUrl = config.http.operatorUrl;
        
        let gameParams: any;
        
        if (gameTokenData.demo) {
            gameParams = createDemoGameParams(
                gameTokenData.gameId,
                gameTokenData.language,
                gameTokenData.platform,
                gameTokenData.trader,
                gameTokenData.lobby,
                gameTokenData.tableId
            );
        } else {
            gameParams = createRealGameParams(
                gameTokenData.currency,
                gameTokenData.customer,
                gameTokenData.gameId,
                gameTokenData.language,
                gameTokenData.platform,
                gameTokenData.token,
                gameTokenData.trader,
                gameTokenData.country,
                gameTokenData.lobby,
                gameTokenData.tableId
            );
        }
        
        const gameUrl = buildGameUrl(`${baseUrl}/casino-engine/game`, gameParams);
        
        log.info("Created game URL for PCES", { 
            customer: gameTokenData.customer,
            gameId: gameTokenData.gameId,
            demo: gameTokenData.demo,
            url: gameUrl
        });
        
        return gameUrl;
    }
}
