import { Modu<PERSON> } from "@nestjs/common";
import { HistoryController } from "@operator/controllers/history.controller";
import { HistoryService } from "@operator/services/history.service";
import { InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import { HttpGateway } from "@skywind-group/sw-integration-core";
import config from "@config";
import { Names } from "@names";

@Module({
    controllers: [HistoryController],
    providers: [
        HistoryService,
        {
            provide: Names.InternalAPIService,
            useFactory: () => {
                return new InternalAPIService(config.pces.roundDetailsUrl);
            }
        },
        {
            provide: HttpGateway,
            useFactory: () => {
                return new HttpGateway(config.http);
            }
        }
    ]
})
export class OperatorModule {}
