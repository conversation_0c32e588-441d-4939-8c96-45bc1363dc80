import { Injectable, Inject } from "@nestjs/common";
import { InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import { PCESHistoryRequest } from "@entities/operator.entities";
import { PCESRoundDetailsResponse } from "@entities/pces.entities";
import { Names } from "@names";
import { buildRoundDetailsUrl } from "@utils/pces.utils";
import config from "@config";
import { logging, measures } from "@skywind-group/sw-utils";

const log = logging.logger("HistoryService");
const { measure } = measures;

@Injectable()
export class HistoryService {
    constructor(
        @Inject(Names.InternalAPIService) 
        private internalAPIService: InternalAPIService
    ) {}

    @measure({ name: "HistoryService.getGameHistoryImageUrl", isAsync: true })
    async getGameHistoryImageUrl(request: PCESHistoryRequest): Promise<string> {
        const details = await this.getGameHistoryDetails(request);
        
        if (details.image && details.image.url) {
            return details.image.url;
        }
        
        // Fallback to building a round details URL
        return this.buildRoundDetailsUrl(request);
    }

    @measure({ name: "HistoryService.getGameHistoryDetails", isAsync: true })
    async getGameHistoryDetails(request: PCESHistoryRequest): Promise<PCESRoundDetailsResponse> {
        try {
            const url = this.buildRoundDetailsUrl(request);
            
            const response = await this.internalAPIService.get<PCESRoundDetailsResponse>(url, {});
            
            if (response.code !== 0) {
                log.warn(`Failed to get round details: ${response.status}`);
                return this.createFallbackResponse(request);
            }
            
            return response;
        } catch (err) {
            log.error(err, "Failed to fetch game history details");
            return this.createFallbackResponse(request);
        }
    }

    private buildRoundDetailsUrl(request: PCESHistoryRequest): string {
        return buildRoundDetailsUrl(config.pces.roundDetailsUrl, {
            customer: request.customer,
            roundId: request.roundId,
            gameId: request.gameId,
            lang: request.lang,
            trader: request.trader
        });
    }

    private createFallbackResponse(request: PCESHistoryRequest): PCESRoundDetailsResponse {
        return {
            code: 0,
            status: "SUCCESS",
            json: {
                gameId: request.gameId,
                gameName: `Game ${request.gameId}`,
                roundId: request.roundId,
                roundDate: new Date().toISOString(),
                betAmount: 0,
                winAmount: 0,
                currency: "USD"
            },
            image: {
                url: `${config.pces.roundDetailsUrl}/default-round-image.png`,
                height: 400,
                width: 600
            }
        };
    }
}
