import { PCESError, pcesErrorCodes, pcesStatusMessages } from "@entities/operator.entities";
import {
    SWError,
    ConnectionError,
    MerchantAdapterAPIError,
    ERROR_LEVEL
} from "@skywind-group/sw-wallet-adapter-core";

// Custom error classes that don't exist in the current version
export class GeneralError extends SWError {
    constructor(message = "General error") {
        super(500, 5000, message, ERROR_LEVEL.ERROR);
    }
}

export class AuthenticateFailedError extends SWError {
    constructor(message = "Authentication failed") {
        super(401, 4010, message, ERROR_LEVEL.WARN);
    }
}

export class InsufficientBalanceError extends SWError {
    constructor(message = "Insufficient balance") {
        super(400, 4001, message, ERROR_LEVEL.WARN);
    }
}

export class TransactionNotFound extends SWError {
    constructor(message = "Transaction not found") {
        super(404, 4040, message, ERROR_LEVEL.WARN);
    }
}

export class ValidationError extends SWError {
    constructor(message = "Validation error") {
        super(400, 4000, message, ERROR_LEVEL.WARN);
    }
}

export function mapPCESToSWError(
    { code, status }: PCESError
): SWError {
    switch (code) {
        case pcesErrorCodes.SUCCESS:
            return new GeneralError("Unexpected success code in error handler");

        case pcesErrorCodes.UNKNOWN_ERROR:
        case pcesErrorCodes.INTERNAL_CACHE_ERROR:
        case pcesErrorCodes.DATA_OUT_OF_RANGE:
            return new GeneralError(status || "Unknown error");

        case pcesErrorCodes.UNAUTHORIZED_REQUEST:
            return new AuthenticateFailedError("Unauthorized request - invalid hash");

        case pcesErrorCodes.NOT_INTEGRATED:
            return new AuthenticateFailedError("Vendor not active");

        case pcesErrorCodes.TOKEN_CUSTOMER_MISMATCH:
            return new AuthenticateFailedError("Token was created for another customer");

        case pcesErrorCodes.UNSUPPORTED_API_VERSION:
            return new ValidationError("Unsupported API version");

        case pcesErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED:
            return new ValidationError("Promotion type not supported");

        case pcesErrorCodes.BET_RECORD_NOT_FOUND:
        case pcesErrorCodes.TRANSACTION_NOT_FOUND:
            return new TransactionNotFound(status);

        case pcesErrorCodes.BET_ALREADY_WON:
        case pcesErrorCodes.BET_ALREADY_SETTLED:
            return new PCESBetAlreadySettledError(status);

        case pcesErrorCodes.AUTHENTICATION_FAILED:
            return new AuthenticateFailedError(status || "Authentication failed");

        case pcesErrorCodes.GAME_NOT_FOUND:
        case pcesErrorCodes.INVALID_GAME:
            return new PCESGameNotFoundError(status);

        case pcesErrorCodes.BET_LIMIT_REACHED:
        case pcesErrorCodes.LOSS_LIMIT_REACHED:
        case pcesErrorCodes.SESSION_LIMIT_REACHED:
        case pcesErrorCodes.PROFIT_LIMIT_REACHED:
            return new PCESLimitReachedError(status);

        case pcesErrorCodes.INVALID_CASINO_VENDOR:
            return new AuthenticateFailedError("Invalid casino vendor");

        case pcesErrorCodes.ALL_BET_ARE_OFF:
            return new PCESBettingOffError(status);

        case pcesErrorCodes.CUSTOMER_NOT_FOUND:
            return new PCESCustomerNotFoundError(status);

        case pcesErrorCodes.INVALID_CURRENCY:
            return new ValidationError("Invalid currency");

        case pcesErrorCodes.INSUFFICIENT_FUNDS:
            return new InsufficientBalanceError(status || "Insufficient funds");

        case pcesErrorCodes.PLAYER_SUSPENDED:
            return new PCESPlayerSuspendedError(status);

        case pcesErrorCodes.REQUIRED_FIELD_MISSING:
            return new ValidationError("Required field missing");

        case pcesErrorCodes.TOKEN_NOT_FOUND:
        case pcesErrorCodes.TOKEN_TIMEOUT:
        case pcesErrorCodes.TOKEN_INVALID:
            return new PCESTokenExpiredError(status);

        case pcesErrorCodes.NEGATIVE_DEPOSIT:
        case pcesErrorCodes.NEGATIVE_WITHDRAWAL:
            return new ValidationError(status || "Invalid amount");

        default:
            return new GeneralError(status || `Unknown PCES error: ${code}`);
    }
}

// Custom PCES-specific errors
export class PCESBetAlreadySettledError extends SWError {
    constructor(message = "Bet already settled") {
        super(400, 40001, message, ERROR_LEVEL.WARN);
    }
}

export class PCESGameNotFoundError extends SWError {
    constructor(message = "Game not found") {
        super(404, 40401, message, ERROR_LEVEL.WARN);
    }
}

export class PCESLimitReachedError extends SWError {
    constructor(message = "Limit reached") {
        super(403, 40301, message, ERROR_LEVEL.WARN);
    }
}

export class PCESBettingOffError extends SWError {
    constructor(message = "Betting is currently off") {
        super(503, 50301, message, ERROR_LEVEL.WARN);
    }
}

export class PCESCustomerNotFoundError extends SWError {
    constructor(message = "Customer not found") {
        super(404, 40402, message, ERROR_LEVEL.WARN);
    }
}

export class PCESPlayerSuspendedError extends SWError {
    constructor(message = "Player is suspended") {
        super(403, 40302, message, ERROR_LEVEL.WARN);
    }
}

export class PCESTokenExpiredError extends SWError {
    constructor(message = "Token expired or invalid") {
        super(401, 40101, message, ERROR_LEVEL.WARN);
    }
}

export class PCESHashValidationError extends SWError {
    constructor(message = "Hash validation failed") {
        super(401, 40102, message, ERROR_LEVEL.ERROR);
    }
}

export class PCESInvalidRequestError extends SWError {
    constructor(message = "Invalid request format") {
        super(400, 40002, message, ERROR_LEVEL.WARN);
    }
}

// Predefined PCES errors for testing
export const pcesInsufficientFunds = (): PCESError => ({
    code: pcesErrorCodes.INSUFFICIENT_FUNDS,
    status: pcesStatusMessages[pcesErrorCodes.INSUFFICIENT_FUNDS]
});

export const pcesTokenExpired = (): PCESError => ({
    code: pcesErrorCodes.TOKEN_TIMEOUT,
    status: pcesStatusMessages[pcesErrorCodes.TOKEN_TIMEOUT]
});

export const pcesAuthenticationFailed = (): PCESError => ({
    code: pcesErrorCodes.AUTHENTICATION_FAILED,
    status: pcesStatusMessages[pcesErrorCodes.AUTHENTICATION_FAILED]
});

export const pcesGameNotFound = (): PCESError => ({
    code: pcesErrorCodes.GAME_NOT_FOUND,
    status: pcesStatusMessages[pcesErrorCodes.GAME_NOT_FOUND]
});

export const pcesBetAlreadySettled = (): PCESError => ({
    code: pcesErrorCodes.BET_ALREADY_SETTLED,
    status: pcesStatusMessages[pcesErrorCodes.BET_ALREADY_SETTLED]
});

export const pcesPlayerSuspended = (): PCESError => ({
    code: pcesErrorCodes.PLAYER_SUSPENDED,
    status: pcesStatusMessages[pcesErrorCodes.PLAYER_SUSPENDED]
});

export const pcesUnauthorizedRequest = (): PCESError => ({
    code: pcesErrorCodes.UNAUTHORIZED_REQUEST,
    status: pcesStatusMessages[pcesErrorCodes.UNAUTHORIZED_REQUEST]
});

// Error condition helpers
export const retryCondition = (err: SWError): boolean => 
    err instanceof ConnectionError ||
    err instanceof GeneralError ||
    err instanceof MerchantAdapterAPIError;

export const rollbackCondition = (err: SWError): boolean =>
    err instanceof ConnectionError ||
    err instanceof GeneralError ||
    err instanceof PCESBettingOffError ||
    err instanceof PCESLimitReachedError ||
    err instanceof PCESPlayerSuspendedError;

export const refundCondition = (err: SWError): boolean =>
    err instanceof PCESTokenExpiredError ||
    err instanceof PCESCustomerNotFoundError ||
    err instanceof AuthenticateFailedError;
