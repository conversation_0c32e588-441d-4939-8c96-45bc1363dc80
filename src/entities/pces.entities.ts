import {
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantStartGameTokenData,
    PaymentRequest
} from "@skywind-group/sw-wallet-adapter-core";
import {
    BrokenGameRequest,
    CommitBonusPaymentRequest,
    GetFreeBetInfoRequest,
    KeepAliveRequest,
    RegulatoryActionRequest,
    TransferRequest
} from "@skywind-group/sw-integration-core";
import { IsDefined, IsIn, IsNotEmpty, IsOptional } from "class-validator";

// PCES-specific enums and interfaces
export enum PCESPlatformType {
    desktop = "d",
    mobile = "m"
}

export enum PCESPromoType {
    FSW = "FSW", // freespin
    JPW = "JPW", // jackpot
    CB = "CB",   // cashBack
    TW = "TW",   // tournament win
    RW = "RW",   // reward
    REW = "REW", // red envelope win
    CDW = "CDW", // cash drop win
    RB = "RB"    // rakeBack
}

export interface PCESGameTokenData {
    customer: string;
    token: string;
    gameId: string;
    currency: string;
    demo: boolean;
    platform: PCESPlatformType;
    language?: string;
    country?: string;
    trader: string;
    tableId?: string;
    lobby?: string;
}

export interface PCESGameLaunchData {
    gameId: string;
    demo: boolean;
    currency: string;
    customer?: string;
    token?: string;
    lang: string;
    platform: PCESPlatformType;
    tableId?: string;
    trader: string;
    lobby?: string;
    country?: string;
}

// Integration-specific entities
export class IntegrationGameLaunchRequest implements PCESGameLaunchData {
    @IsDefined()
    @IsNotEmpty()
    gameId: string;

    @IsDefined()
    demo: boolean;

    @IsDefined()
    @IsNotEmpty()
    currency: string;

    @IsOptional()
    @IsNotEmpty()
    customer?: string;

    @IsOptional()
    @IsNotEmpty()
    token?: string;

    @IsDefined()
    @IsNotEmpty()
    lang: string;

    @IsIn([PCESPlatformType.desktop, PCESPlatformType.mobile])
    platform: PCESPlatformType;

    @IsOptional()
    @IsNotEmpty()
    tableId?: string;

    @IsDefined()
    @IsNotEmpty()
    trader: string;

    @IsOptional()
    @IsNotEmpty()
    lobby?: string;

    @IsOptional()
    @IsNotEmpty()
    country?: string;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData, PCESGameTokenData {
    relaunchUrl?: string;
    loginFailed?: boolean;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData, PCESGameTokenData {}

export interface IntegrationInitRequest extends MerchantGameInitRequest, PCESGameLaunchData {
    previousStartTokenData?: IntegrationStartGameTokenData;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
    promoType?: PCESPromoType;
    promoRef?: string;
    freeSpinData?: PCESFreeSpinData;
}

export interface IntegrationGetBalanceRequest {
    gameTokenData: IntegrationGameTokenData;
    merchantInfo: any;
}

export interface IntegrationTransferRequest extends TransferRequest<IntegrationGameTokenData> {}

export interface IntegrationKeepAliveRequest extends KeepAliveRequest<IntegrationGameTokenData> {}

export interface IntegrationBrokenGameRequest extends BrokenGameRequest<IntegrationGameTokenData> {}

export interface IntegrationGetFreeBetInfoRequest extends GetFreeBetInfoRequest<IntegrationGameTokenData> {}

export interface IntegrationRegulatoryActionRequest extends RegulatoryActionRequest<IntegrationGameTokenData> {}

// PCES-specific interfaces
export interface PCESFreeSpinData {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

export interface PCESFreespinRequest {
    customers: string[];
    games: string[];
    numberOfFreespins: number;
    freespinRef: string;
    wagerRequirement: number;
    validFrom: Date;
    validUntil: Date;
    maxWin?: number;
    costPerBet?: number;
    coinValueLevel?: number;
    lines?: number;
    coins?: number;
    denomination?: number;
    betPerLine?: number;
}

export interface PCESRoundDetailsRequest {
    customer: string;
    roundId: string;
    gameId: string;
    lang: string;
    trader?: string;
}

export interface PCESRoundDetailsResponse {
    code: number;
    status: string;
    json?: {
        gameId: string;
        gameName: string;
        roundId: string;
        roundDate: string;
        betAmount: number;
        winAmount: number;
        currency: string;
    };
    image?: {
        url: string;
        height?: number;
        width?: number;
    };
    html?: {
        content: string;
        height?: number;
        width?: number;
    };
}
